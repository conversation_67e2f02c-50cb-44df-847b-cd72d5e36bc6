# CPU 任务调度问题

## 问题描述
给定一个任务数组 `tasks` 和一个冷却时间 `n`，需要计算完成所有任务的最少时间。

### 约束条件
- 相同类型的任务之间必须有至少 `n` 个时间单位的冷却时间
- 在冷却期间，CPU可以执行其他类型的任务或者空闲
- 任务类型用大写字母 A-Z 表示

## 解题思路

### 方法一：模拟法（使用优先队列）
1. 统计每个任务的出现频次
2. 使用最大堆存储任务频次
3. 模拟执行过程：
   - 在每个冷却周期（n+1个时间单位）内，尽可能多地执行不同类型的任务
   - 优先执行频次最高的任务
   - 如果任务执行后还有剩余，将剩余频次重新放入堆中

**时间复杂度**: O(time)，其中 time 是总执行时间
**空间复杂度**: O(1)，最多26种不同的任务类型

### 方法二：数学优化法
1. 统计每个任务的频次并排序
2. 找到出现频次最高的任务
3. 计算理论上的空闲时间
4. 用其他任务填充空闲时间
5. 如果空闲时间被完全填充，则总时间就是任务总数；否则需要加上剩余的空闲时间

**时间复杂度**: O(n)，其中 n 是任务数量
**空间复杂度**: O(1)

## 示例

### 示例1
```
输入: tasks = ["A","A","A","B","B","B"], n = 2
输出: 8
解释: A -> B -> idle -> A -> B -> idle -> A -> B
```

### 示例2
```
输入: tasks = ["A","A","A","B","B","B"], n = 0
输出: 6
解释: 没有冷却时间，可以连续执行
```

### 示例3
```
输入: tasks = ["A","A","A","A","A","A","B","C","D","E","F","G"], n = 2
输出: 16
解释: 一种可能的执行顺序是 A -> B -> C -> A -> D -> E -> A -> F -> G -> A -> idle -> idle -> A -> idle -> idle -> A
```

## 编译和运行

```bash
# 编译
g++ -o task_scheduler task_scheduler.cpp

# 运行
./task_scheduler
```

## 核心算法分析

### 关键观察
1. 出现频次最高的任务决定了最少的执行时间
2. 如果有足够多的不同类型任务，可以完全填充冷却时间
3. 最终时间 = max(任务总数, (最高频次-1) * (n+1) + 最高频次任务的种类数)

### 边界情况
- n = 0：没有冷却时间，直接返回任务总数
- 只有一种任务：需要考虑冷却时间
- 任务种类很多：可能不需要空闲时间
