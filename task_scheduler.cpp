#include <iostream>
#include <vector>
#include <unordered_map>
#include <queue>
#include <algorithm>
using namespace std;

class Solution {
public:
    int leastInterval(vector<char>& tasks, int n) {
        // 统计每个任务的频次
        unordered_map<char, int> taskCount;
        for (char task : tasks) {
            taskCount[task]++;
        }
        
        // 使用优先队列（最大堆）存储任务频次
        priority_queue<int> maxHeap;
        for (auto& pair : taskCount) {
            maxHeap.push(pair.second);
        }
        
        int time = 0;
        
        // 模拟任务执行过程
        while (!maxHeap.empty()) {
            vector<int> temp;
            int cycle = n + 1; // 一个完整的冷却周期
            
            // 在一个冷却周期内尽可能多地执行任务
            for (int i = 0; i < cycle && (!maxHeap.empty() || !temp.empty()); i++) {
                if (!maxHeap.empty()) {
                    int count = maxHeap.top();
                    maxHeap.pop();
                    if (count > 1) {
                        temp.push_back(count - 1);
                    }
                }
                time++;
            }
            
            // 将剩余的任务重新放回堆中
            for (int count : temp) {
                maxHeap.push(count);
            }
        }
        
        return time;
    }
    
    // 优化版本：数学方法
    int leastIntervalOptimized(vector<char>& tasks, int n) {
        vector<int> count(26, 0);
        for (char task : tasks) {
            count[task - 'A']++;
        }
        
        sort(count.begin(), count.end());
        
        int maxCount = count[25];
        int idleTime = (maxCount - 1) * n;
        
        for (int i = 24; i >= 0 && count[i] > 0; i--) {
            idleTime -= min(count[i], maxCount - 1);
        }
        
        return idleTime > 0 ? idleTime + tasks.size() : tasks.size();
    }
};

// 测试函数
void testSolution() {
    Solution solution;
    
    // 测试用例1
    vector<char> tasks1 = {'A', 'A', 'A', 'B', 'B', 'B'};
    int n1 = 2;
    cout << "测试用例1: ";
    for (char c : tasks1) cout << c << " ";
    cout << ", n = " << n1 << endl;
    cout << "结果: " << solution.leastInterval(tasks1, n1) << endl;
    cout << "优化结果: " << solution.leastIntervalOptimized(tasks1, n1) << endl;
    cout << endl;
    
    // 测试用例2
    vector<char> tasks2 = {'A', 'A', 'A', 'B', 'B', 'B'};
    int n2 = 0;
    cout << "测试用例2: ";
    for (char c : tasks2) cout << c << " ";
    cout << ", n = " << n2 << endl;
    cout << "结果: " << solution.leastInterval(tasks2, n2) << endl;
    cout << "优化结果: " << solution.leastIntervalOptimized(tasks2, n2) << endl;
    cout << endl;
    
    // 测试用例3
    vector<char> tasks3 = {'A', 'A', 'A', 'A', 'A', 'A', 'B', 'C', 'D', 'E', 'F', 'G'};
    int n3 = 2;
    cout << "测试用例3: ";
    for (char c : tasks3) cout << c << " ";
    cout << ", n = " << n3 << endl;
    cout << "结果: " << solution.leastInterval(tasks3, n3) << endl;
    cout << "优化结果: " << solution.leastIntervalOptimized(tasks3, n3) << endl;
}

int main() {
    cout << "=== CPU 任务调度问题 ===" << endl;
    cout << "问题描述：给定一个任务数组和冷却时间n，计算完成所有任务的最少时间" << endl;
    cout << "相同类型的任务之间必须有至少n个时间单位的冷却时间" << endl;
    cout << endl;
    
    testSolution();
    
    return 0;
}
